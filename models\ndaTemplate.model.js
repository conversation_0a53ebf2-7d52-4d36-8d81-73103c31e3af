const history = require("../models/plugins/history.plugin");

module.exports = (sequelize, DataTypes) => {
  const NdaTemplate = sequelize.define(
    "NdaTemplate",
    {
      nda_template_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      name: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      version: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      document_url: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      jurisdiction: {
        type: DataTypes.STRING(100),
        allowNull: true,
      },
    },
    {
      tableName: "nda_template",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          unique: true,
          fields: ["name", "version"],
        },
      ],
    }
  );

  NdaTemplate.associate = (models) => {
    NdaTemplate.hasMany(models.NdaAgreement, {
      foreignKey: "nda_template_id",
      as: "agreements",
    });
  };

  history(NdaTemplate, sequelize, DataTypes);

  return NdaTemplate;
};
