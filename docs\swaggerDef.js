const { version } = require('../package.json');
const config = require('../config/config');
const customServer = [
  {
    url: config.server_url.default,
    description: 'Development server for all routes',
  }];
const tykServers = [
  {
    url: config.server_url.protected,
    description: 'Development server for protected routes',
  },
  {
    url: config.server_url.public,
    description: 'Development server for public routes',
  },
];

const swaggerDef = {
  openapi: '3.0.0',
  info: {
    title: 'CareMate API',
    version: version || '1.0.0',
    description: 'CareMate API'
  },
  servers: config.auth.mode === "tyk" ? tykServers : customServer,
};

module.exports = swaggerDef;
