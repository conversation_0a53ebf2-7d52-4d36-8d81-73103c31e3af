const passport = require('../config/passport');
const { status: httpStatus } = require("http-status"); 
const { ApiError } = require("../helpers/api.helper");
const config = require("../config/config");
const { Identity } = require("../models");
const tykService = require("../services/tyk.service");
const logger = require('../config/logger');

/**
 * Enhanced Tyk header validation with comprehensive security
 * @param {Object} req - Express request object
 * @param {Array} requiredRights - Required permissions
 * @returns {Promise<Object>} - Identity object with permissions
 */
const validateTykHeaders = async (req, requiredRights) => {

  logger.info(`Tyk validation started for ${req.method} ${req.path}`);

  // Step 1: Validate IP address
  const clientIP = req.ip || req.connection.remoteAddress || req.socket.remoteAddress;
  logger.debug(`Client IP: ${clientIP}`);

  if (!tykService.validateIP(clientIP)) {
    logger.warn(`Blocked request from unauthorized IP: ${clientIP}`);
    throw new ApiError(httpStatus.FORBIDDEN, "Request not from authorized gateway IP");
  }

  // Step 2: Extract and validate security headers
  const encryptedIdentityId = req.headers['x-caremate-identity-id'];
  const encryptedPermissions = req.headers['x-caremate-permissions'];
  const authorized = req.headers['x-caremate-authorized'];
  const timestamp = req.headers['x-caremate-timestamp'];
  const nonce = req.headers['x-caremate-nonce'];
  const signature = req.headers['x-caremate-signature'];
  const gatewayId = req.headers['x-caremate-gateway-id'];



  logger.info(`Tyk headers: identity=${encryptedIdentityId}, permissions=${encryptedPermissions}, authorized=${authorized}, timestamp=${timestamp}, nonce=${nonce}, signature=${signature}, gatewayId=${gatewayId}`);

  if (!encryptedIdentityId || !encryptedPermissions || authorized !== 'true' || !timestamp || !nonce || !signature || gatewayId !== 'tyk-gateway-v1') {
    logger.warn('Invalid or missing Tyk authentication headers');
    throw new ApiError(httpStatus.UNAUTHORIZED, "Invalid or missing Tyk authentication headers");
  }

  // Step 3: Validate timestamp to prevent replay attacks
  if (!tykService.validateTimestamp(timestamp)) {
    logger.warn(`Request with invalid timestamp: ${timestamp}`);
    throw new ApiError(httpStatus.UNAUTHORIZED, "Request timestamp is invalid or too old");
  }

  // Step 4: Verify request signature (using improved HMAC matching for Tyk compatibility)
  const method = req.method.toUpperCase();
  const path = req.path;
  const originalUrl = req.originalUrl;

  logger.debug(`Signature verification data: method=${method}, path=${path}, originalUrl=${originalUrl}, timestamp=${timestamp}, nonce=${nonce}`);

  // Try different path variations to match Tyk's path handling
  const pathVariations = [
    path,                                    // Express path
    originalUrl.split('?')[0],              // Original URL without query
    originalUrl.replace('/api', ''),        // Remove /api prefix
    path.replace('/api', ''),               // Remove /api from path
  ];

  let isValidStronger = false;
  let isValidSimple = false;
  let validPath = null;

  // Try each path variation
  for (const testPath of pathVariations) {
    const signatureData = `${method}|${testPath}|${timestamp}|${nonce}`;
    logger.debug(`Testing signature with data: ${signatureData}`);

    isValidStronger = tykService.verifyStrongerSignature(signature, signatureData);
    if (isValidStronger) {
      validPath = testPath;
      logger.debug(`Stronger signature valid with path: ${testPath}`);
      break;
    }

    isValidSimple = tykService.verifySimpleSignature(signature, signatureData);
    if (isValidSimple) {
      validPath = testPath;
      logger.debug(`Simple signature valid with path: ${testPath}`);
      break;
    }
  }

  if (!isValidStronger && !isValidSimple) {
    logger.warn(`Request signature verification failed for ${method} ${path}`);
    logger.warn(`Tried paths: ${pathVariations.join(', ')}`);
    logger.warn(`Expected signature format: METHOD|PATH|TIMESTAMP|NONCE`);
    throw new ApiError(httpStatus.UNAUTHORIZED, "Request signature verification failed");
  }

  if (isValidStronger) {
    logger.debug(`Using improved signature verification with path: ${validPath}`);
  } else {
    logger.debug(`Using legacy signature verification with path: ${validPath}`);
  }

  // Step 5: Extract identity and permissions (temporarily bypass decryption)
  // TODO: Fix header decryption to match Tyk encryption exactly
  let identityId, permissions;

  // Temporarily extract from JWT token instead of encrypted headers
  const authHeader = req.headers.authorization;
  if (authHeader && authHeader.startsWith('Bearer ')) {
    const token = authHeader.substring(7);
    try {
      const tokenParts = token.split('.');
      if (tokenParts.length === 3) {
        const base64Payload = tokenParts[1];
        const paddedPayload = base64Payload + '='.repeat((4 - base64Payload.length % 4) % 4);
        const decodedPayload = Buffer.from(paddedPayload, 'base64').toString('utf8');
        const payload = JSON.parse(decodedPayload);

        identityId = payload.sub;
        permissions = payload.permissions || [];

        logger.debug(`Extracted from JWT: identity=${identityId}, permissions=${permissions.length}`);
      }
    } catch (jwtError) {
      logger.warn('Failed to extract from JWT:', jwtError.message);
      throw new ApiError(httpStatus.UNAUTHORIZED, "Failed to extract user data");
    }
  }

  if (!identityId || !permissions) {
    throw new ApiError(httpStatus.UNAUTHORIZED, "Failed to extract user data");
  }

  if (!identityId || !Array.isArray(permissions)) {
    logger.warn('Invalid decrypted header data');
    throw new ApiError(httpStatus.UNAUTHORIZED, "Invalid decrypted header data");
  }

  // Step 6: Validate identity exists in database (skip for public requests)
  let identity = null;
  if (identityId !== 'public') {
    identity = await Identity.findByPk(identityId);
    if (!identity) {
      logger.warn(`Identity not found in database: ${identityId}`);
      throw new ApiError(httpStatus.UNAUTHORIZED, "Identity not found in database");
    }
  } else {
    // Create a mock identity object for public requests
    identity = {
      id: 'public',
      email: 'public',
      permissions: permissions
    };
  }

  // Add permissions to identity object
  identity.permissions = permissions;

  // Step 7: Check required permissions
  if (requiredRights.length > 0) {
    const hasRequiredRights = requiredRights.every((requiredRight) =>
      permissions.includes(requiredRight)
    );

    if (!hasRequiredRights && req.params.identityId !== identityId) {
      logger.warn(`Insufficient permissions for ${identityId}. Required: ${requiredRights.join(', ')}, Has: ${permissions.join(', ')}`);
      throw new ApiError(httpStatus.FORBIDDEN, "Insufficient permissions for this operation");
    }
  }

  logger.info(`Tyk authentication successful for identity: ${identityId}`);
  return identity;
};

/**
 * The verifyCallback for custom JWT authentication
 */
const verifyCallback = (req, resolve, reject, requiredRights) => async (err, identity, info) => {
  if (err || info || !identity) {
    logger.warn('JWT authentication failed:', { err: err?.message, info });
    return reject(
      new ApiError(httpStatus.UNAUTHORIZED, "Please authenticate")
    );
  }

  req.identity = identity;

  if (requiredRights.length) {
    // Get permissions directly from the JWT token (added by passport strategy)
    const identityRights = identity.permissions || [];

    if (identityRights.length === 0) {
      logger.warn(`No permissions assigned to identity: ${identity.identity_id}`);
      return reject(
        new ApiError(httpStatus.FORBIDDEN, "No permissions assigned to identity")
      );
    }

    // Check required rights
    const hasRequiredRights = requiredRights.every((requiredRight) =>
      identityRights.includes(requiredRight)
    );

    if (
      !hasRequiredRights &&
      req.params.identityId !== identity.identity_id
    ) {
      logger.warn(`Unauthorized access attempt by identity: ${identity.identity_id}`);
      return reject(new ApiError(httpStatus.FORBIDDEN, "Unauthorized access of identity"));
    }
  }

  logger.info(`JWT authentication successful for identity: ${identity.identity_id}`);
  resolve();
};

/**
 * IP restriction middleware for Tyk mode
 * Only allows requests from authorized gateway IPs when in Tyk mode
 */
const ipRestriction = (req, res, next) => {
  // Only apply IP restrictions in Tyk mode
  if (config.auth.mode !== 'tyk') {
    return next();
  }

  // Skip IP restriction for health check endpoint
  if (req.path === '/api/health') {
    return next();
  }

  // Get client IP address
  const clientIP = req.ip || 
                   req.connection.remoteAddress || 
                   req.socket.remoteAddress ||
                   (req.connection.socket ? req.connection.socket.remoteAddress : null);

  // Validate IP against allowed list
  if (!tykService.validateIP(clientIP)) {
    const error = new ApiError(
      httpStatus.FORBIDDEN, 
      `Access denied from IP: ${clientIP}. Only Tyk gateway is allowed.`
    );
    return next(error);
  }

  next();
};

/**
 * Main authentication middleware that supports both custom and Tyk modes
 * @param {...string} requiredRights - Required permissions for the endpoint
 * @returns {Function} - Express middleware function
 */
const auth = (...requiredRights) =>
  async (req, res, next) => {
    try {
      // Check authentication mode from configuration
      if (config.auth.mode === 'tyk') {
        // Tyk Gateway mode - validate headers set by Tyk
        const identity = await validateTykHeaders(req, requiredRights);
        req.identity = identity;
        return next();
      } else {
        // Custom mode - use existing JWT authentication
        return new Promise((resolve, reject) => {
          passport.authenticate(
            "custom",
            { session: false },
            verifyCallback(req, resolve, reject, requiredRights)
          )(req, res, next);
        })
          .then(() => next())
          .catch((err) => next(err));
      }
    } catch (error) {
      logger.error('Authentication middleware error:', error);
      return next(error);
    }
  };

/**
 * Middleware to validate Tyk headers without authentication
 * Useful for debugging and testing
 */
const validateTykHeadersOnly = (req, res, next) => {
  if (config.auth.mode !== 'tyk') {
    return next();
  }

  try {
    const headerValidation = tykService.validateTykHeaders(req.headers);
    req.tykValidation = headerValidation;
    logger.info('Tyk headers validated successfully');
    next();
  } catch (error) {
    logger.error('Tyk header validation failed:', error);
    next(new ApiError(httpStatus.UNAUTHORIZED, error.message));
  }
};

// Export auth as default function for backward compatibility
module.exports = auth;

// Also export named functions
module.exports.auth = auth;
module.exports.ipRestriction = ipRestriction;
module.exports.validateTykHeaders = validateTykHeaders;
module.exports.validateTykHeadersOnly = validateTykHeadersOnly;
