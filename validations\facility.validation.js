const Joi = require('joi');
const { unique, exists, existsMasterData } = require('./custom.validation');
const facilityId = Joi.string().required().external(exists("Facility", "facility_id"));
const { State } = require('../models'); // Adjust the import based on your project structure

// Helper function to validate state-country relationship (UUIDs)
const validateStateCountry = async (state_id, country_id) => {
    console.log("State ID:", state_id);
  const state = await State.findByPk(state_id);

  if (!state) {
    throw new Error("State not found.");
  }
  
  // Directly compare UUID strings
  if (state.country_id !== country_id) {  // Ensure spelling matches your DB column!
    throw new Error("State does not belong to the selected country.");
  }
};



const stateId = Joi.string().optional().external(async (value, helpers) => {
  console.log("State ID function");
  if (!value) return value;
  

  const state = await exists("State", "state_id")(value);
  if (!state) {
    throw new Error("State does not exist.");
  }

  const countryId = helpers.state.ancestors[1].country_id;
  if (state.country_id !== countryId) {
    throw new Error("State does not belong to the given country.");
  }

  return value;
});

const create = {
  body: Joi.object().keys({
    status: Joi.number().integer().external(existsMasterData("facility_status")).required(),
    image: Joi.string().uri().optional()
    // .allow(null, "")
    ,
    name: Joi.string().required(),
    facility_code: Joi.string().optional().external(unique('Facility', 'facility_code')),
    facility_type: Joi.number().integer().external(existsMasterData("facility_type")).optional(),
    timezone_id: Joi.string().optional().external(exists("Timezone", "timezone_id")),
    phone: Joi.string().optional(),
    email: Joi.string().email().optional(),
    geo_location_code: Joi.number().precision(7).optional(),
    other_code: Joi.string().optional(),
    facility_url: Joi.string().uri().optional(),
    connected_applications: Joi.string().optional(),
    notes: Joi.string().optional(),
    address: Joi.object().keys({
      address_line_1: Joi.string().required(),
      address_line_2: Joi.string().optional(),
      postal_code: Joi.number().optional(),
      map_url: Joi.string().uri().optional(),
      region: Joi.string().optional(),
      country_id: Joi.string().optional().external(exists("Country", "country_id")),
      state_id: stateId,
    }),
  }),
};

const facility = {
  params: Joi.object().keys({
    facilityId,
  }),
};

// update = put
const update = {
  params: Joi.object().keys({
    facilityId,
  }),
  body: Joi.object()
    .keys({
      name: Joi.string().optional(),
      image: Joi.string().uri().optional().allow(null, ""),
      facility_code: Joi.string().optional(),
      facility_type: Joi.number().integer().external(existsMasterData("facility_type")).optional(),
      timezone_id: Joi.string().optional().external(exists("Timezone", "timezone_id")),
      phone: Joi.string().optional(),
      email: Joi.string().email().optional(),
      geo_location_code: Joi.number().precision(7).optional(),
      other_code: Joi.string().optional(),
      facility_url: Joi.string().uri().optional(),
      connected_applications: Joi.string().optional(),
      notes: Joi.string().optional(),
      status: Joi.number().integer().external(existsMasterData("facility_status")).required(),
    })
    .min(1),
};

// for one field = patch
const status = {
  params: Joi.object().keys({
    facilityId,
  }),
  body: Joi.object().keys({
    status: Joi.number().integer().external(existsMasterData("facility_status")).required(),
  }),
};

const address = {
  params: Joi.object().keys({
    facilityId,
  }),
  body: Joi.object().keys({
    address_line_1: Joi.string().required(),
    address_line_2: Joi.string().optional(),
    postal_code: Joi.string().required(),
    map_url: Joi.string().uri().optional(),
    region: Joi.string().optional(),
    country_id: Joi.string().optional().external(exists("State", "country_id")),
    state_id: Joi.string().optional().external(exists("State", "state_id")),
  }),
};

const image = {
  params: Joi.object().keys({
    facilityId,
  }),
  body: Joi.object().keys({
    image: Joi.string().uri().required(),
  }),
};

module.exports = {
  create,
  facility,
  update,
  status,
  address,
  image,
};
