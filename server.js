const config = require("./config/config");
const logger = require("./config/logger");
const connectRabbitmq = require("./config/rabbitmq");

let server;
let connection, channel = global.channel;
let models;

(async () => {
  // Await RabbitMQ connection first
  if (config.messageQueuing.status && !channel) {
    const rabbit = await connectRabbitmq();
    if (!rabbit || !rabbit.connection || !rabbit.channel) {
      throw new Error("RabbitMQ connect didn't return a valid object");
    }
    connection, channel = rabbit.connection, rabbit.channel;
  }

  global.APP_CONTEXT = { function: null, application: "api" };

  // Now require models after RabbitMQ is ready
  models = require("./models");
  const app = require("./app");

  // Authenticate and start the server
  models.sequelize.authenticate().then(async () => {
    logger.info("Connected to PostgreSQL");
    server = app.listen(config.port, '0.0.0.0', () => {
      logger.info(`Listening to port ${config.port} on all interfaces`);
    });
  });
})();

const closeDatabaseConnection = async () => {
  if (models) {
    await models.sequelize.close();
    logger.info("Database connection closed");
  } else {
    logger.warn("No database connection to close");
  }
};

const exitHandler = async () => {
  if (server) {
    server.close(async err => {
      if (err) logger.error("Error closing server:", err);
      else logger.info("HTTP server closed");
    });
  }
  if (channel) await channel.close().catch(e => logger.warn("RabbitMQ channel close failed:", e));
  if (connection) await connection.close().catch(e => logger.warn("RabbitMQ conn close failed:", e));
  await closeDatabaseConnection();
  logger.info("Server closed");
  process.exit(0);
};

const unexpectedErrorHandler = async (error) => {
  logger.error(error);
  await exitHandler();
};

process.on("uncaughtException", unexpectedErrorHandler);
process.on("unhandledRejection", unexpectedErrorHandler);
process.on("SIGTERM", async () => {
  logger.info("SIGTERM received");
  if (server) {
    await exitHandler();
  }
});
process.on("SIGINT", async () => {
  logger.info("SIGINT received");
  if (server) {
    await exitHandler();
  }
});
